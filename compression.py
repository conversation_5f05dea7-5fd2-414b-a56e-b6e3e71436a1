"""
Compression module for handling file and folder compression
"""
import subprocess
import logging
import os
import shutil
from pathlib import Path
from typing import Dict, Any, Optional
import tempfile

from config import COMPRESSION_SCRIPT, COMPRESSION_EXTENSION, OUTPUT_DIR, ensure_directories

logger = logging.getLogger(__name__)

class CompressionError(Exception):
    """Custom exception for compression errors"""
    pass

def format_size(size_bytes: int) -> str:
    """Convert bytes to human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f}{unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f}PB"

def get_file_size(path: Path) -> int:
    """Get size of file or directory in bytes"""
    if path.is_file():
        return path.stat().st_size
    elif path.is_dir():
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                try:
                    total_size += os.path.getsize(filepath)
                except (OSError, FileNotFoundError):
                    # Skip files that can't be accessed
                    continue
        return total_size
    return 0

def compress_file(input_path: str, output_filename: Optional[str] = None, max_compress: bool = False) -> Dict[str, Any]:
    """
    Compress a single file or directory using the compression script

    Args:
        input_path: Path to the file or directory to compress
        output_filename: Optional custom output filename
        max_compress: If True, use maximum compression (-m5 flag)

    Returns:
        Dictionary containing compression results
    """
    ensure_directories()
    
    input_path_obj = Path(input_path)
    if not input_path_obj.exists():
        raise CompressionError(f"Input path does not exist: {input_path}")
    
    # Generate output filename
    if output_filename is None:
        base_name = input_path_obj.name
        output_filename = f"{base_name}{COMPRESSION_EXTENSION}"
    
    output_path = OUTPUT_DIR / output_filename
    
    # Get original size before compression
    original_size = get_file_size(input_path_obj)
    
    try:
        # Run the compression script
        compression_mode = "maximum" if max_compress else "default"
        logger.info(f"Starting {compression_mode} compression of {input_path} to {output_path}")

        # Prepare command arguments
        cmd_args = [str(COMPRESSION_SCRIPT), str(input_path), str(output_path)]
        if max_compress:
            cmd_args.append("true")  # Pass "true" for max compression
        else:
            cmd_args.append("false")  # Pass "false" for default compression

        result = subprocess.run(
            cmd_args,
            capture_output=True,
            text=True,
            check=True,
            timeout=300  # 5 minute timeout
        )
        
        # Parse the script output
        output_lines = result.stdout.strip().split('\n')
        compression_data = {}
        
        for line in output_lines:
            if ':' in line and any(key in line for key in ['ORIGINAL_SIZE', 'COMPRESSED_SIZE', 'COMPRESSION_RATIO', 'OUTPUT_PATH']):
                key, value = line.split(':', 1)
                compression_data[key] = value
        
        # Verify output file exists
        if not output_path.exists():
            raise CompressionError("Compression script completed but output file not found")
        
        compressed_size = output_path.stat().st_size
        compression_ratio = ((original_size - compressed_size) / original_size * 100) if original_size > 0 else 0
        
        return {
            "status": "success",
            "output_path": str(output_path),
            "original_size": format_size(original_size),
            "compressed_size": format_size(compressed_size),
            "compression_ratio": f"{compression_ratio:.1f}%",
            "original_size_bytes": original_size,
            "compressed_size_bytes": compressed_size
        }
        
    except subprocess.TimeoutExpired:
        raise CompressionError("Compression timed out after 5 minutes")
    except subprocess.CalledProcessError as e:
        error_msg = f"Compression script failed: {e.stderr}"
        logger.error(error_msg)
        raise CompressionError(error_msg)
    except Exception as e:
        logger.error(f"Unexpected error during compression: {str(e)}")
        raise CompressionError(f"Compression failed: {str(e)}")

def compress_multiple_files(file_paths: list, output_filename: Optional[str] = None, max_compress: bool = False) -> Dict[str, Any]:
    """
    Compress multiple files by creating a temporary directory and compressing it

    Args:
        file_paths: List of file paths to compress
        output_filename: Optional custom output filename
        max_compress: If True, use maximum compression (-m5 flag)

    Returns:
        Dictionary containing compression results
    """
    ensure_directories()
    
    # Create a temporary directory to hold all files
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        batch_dir = temp_path / "batch_compression"
        batch_dir.mkdir()
        
        total_original_size = 0
        
        # Copy all files to the temporary directory
        for file_path in file_paths:
            source_path = Path(file_path)
            if not source_path.exists():
                raise CompressionError(f"File does not exist: {file_path}")
            
            dest_path = batch_dir / source_path.name
            if source_path.is_file():
                shutil.copy2(source_path, dest_path)
            elif source_path.is_dir():
                shutil.copytree(source_path, dest_path)
            
            total_original_size += get_file_size(source_path)
        
        # Generate output filename if not provided
        if output_filename is None:
            output_filename = f"batch_compression{COMPRESSION_EXTENSION}"
        
        # Compress the batch directory
        return compress_file(str(batch_dir), output_filename, max_compress)
