{"info": {"_postman_id": "zmt-compression-api", "name": "ZMT Compression API", "description": "Complete API collection for the ZMT Lossless Compression Service - Phase 1\n\n## Overview\nThis API provides endpoints for compressing and decompressing files using the ZMT compression algorithm. It supports both file uploads and local path operations.\n\n## Base URL\n`http://localhost:8000`\n\n## Features\n- File and directory compression with ZMT algorithm\n- File upload support (multipart/form-data)\n- Local path compression\n- Archive decompression and restoration\n- Compression statistics and ratios\n- Health monitoring\n\n## Authentication\nNo authentication required for Phase 1 (local development)\n\n## Error Handling\n- 200: Success\n- 400: Bad request (missing parameters)\n- 404: File/archive not found\n- 413: File size exceeds limits\n- 422: Validation error\n- 500: Internal server error", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health Check", "item": [{"name": "Get API Status", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has correct structure\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData).to.have.property('message');", "    pm.expect(jsonData.status).to.eql('ok');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/status", "host": ["{{base_url}}"], "path": ["api", "status"]}, "description": "Check if the API is running and healthy.\n\n**Response:**\n```json\n{\n  \"status\": \"ok\",\n  \"message\": \"Compression API is running\"\n}\n```"}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/status", "host": ["{{base_url}}"], "path": ["api", "status"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"status\": \"ok\",\n  \"message\": \"Compression API is running\"\n}"}]}], "description": "Endpoints for checking API health and status"}, {"name": "Compression", "item": [{"name": "Compress File Upload", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has compression data\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData).to.have.property('output_path');", "    pm.expect(jsonData).to.have.property('original_size');", "    pm.expect(jsonData).to.have.property('compressed_size');", "    pm.expect(jsonData).to.have.property('compression_ratio');", "    pm.expect(jsonData.status).to.eql('success');", "});", "", "// Store the output path for decompression test", "const jsonData = pm.response.json();", "pm.environment.set('last_compressed_file', jsonData.output_path);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": [], "description": "Select one or more files to compress"}, {"key": "output_filename", "value": "my_archive.zmt", "description": "Optional custom output filename", "type": "text", "disabled": true}, {"key": "maxCompress", "value": "true", "description": "Enable maximum compression (-m5 flag)", "type": "text", "disabled": true}]}, "url": {"raw": "{{base_url}}/api/compress", "host": ["{{base_url}}"], "path": ["api", "compress"]}, "description": "Compress files by uploading them via multipart/form-data.\n\n**Parameters:**\n- `files` (file, required): One or more files to compress\n- `output_filename` (string, optional): Custom name for the output archive\n- `maxCompress` (boolean, optional): Enable maximum compression with -m5 flag (default: false)\n\n**Response:**\n```json\n{\n  \"status\": \"success\",\n  \"output_path\": \"/tmp/compression_api/outputs/file.zmt\",\n  \"original_size\": \"1.0MB\",\n  \"compressed_size\": \"1.1KB\",\n  \"compression_ratio\": \"99.9%\",\n  \"original_size_bytes\": 1048576,\n  \"compressed_size_bytes\": 1127\n}\n```\n\n**Note:** Select files using the file picker in the form-data section. Enable `maxCompress` for better compression ratios at the cost of processing time."}, "response": []}, {"name": "Compress Local Path", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has compression data\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData).to.have.property('output_path');", "    pm.expect(jsonData.status).to.eql('success');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "input_path", "value": "/path/to/your/file.txt", "description": "Absolute path to file or directory to compress"}, {"key": "output_filename", "value": "custom_name.zmt", "description": "Optional custom output filename", "disabled": true}, {"key": "maxCompress", "value": "true", "description": "Enable maximum compression (-m5 flag)", "disabled": true}]}, "url": {"raw": "{{base_url}}/api/compress", "host": ["{{base_url}}"], "path": ["api", "compress"]}, "description": "Compress a file or directory by specifying its local path.\n\n**Parameters:**\n- `input_path` (string, required): Absolute path to file or directory\n- `output_filename` (string, optional): Custom name for the output archive\n- `maxCompress` (boolean, optional): Enable maximum compression with -m5 flag (default: false)\n\n**Example paths:**\n- `/Users/<USER>/Documents/file.txt` (single file)\n- `/Users/<USER>/Documents/folder` (entire directory)\n\n**Folder Compression:**\nThe API supports compressing entire directories. Simply provide the path to a folder and ZMT will compress all contents recursively.\n\n**Response:**\n```json\n{\n  \"status\": \"success\",\n  \"output_path\": \"/tmp/compression_api/outputs/file.zmt\",\n  \"original_size\": \"180.0MB\",\n  \"compressed_size\": \"70.0MB\",\n  \"compression_ratio\": \"61.1%\",\n  \"original_size_bytes\": 188743680,\n  \"compressed_size_bytes\": 73400320\n}\n```"}, "response": []}], "description": "Endpoints for compressing files and directories using ZMT algorithm"}, {"name": "Decompression", "item": [{"name": "Decompress Archive Upload", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has decompression data\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData).to.have.property('restored_path');", "    pm.expect(jsonData).to.have.property('extracted_files');", "    pm.expect(jsonData).to.have.property('total_size');", "    pm.expect(jsonData.status).to.eql('success');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "archive_file", "type": "file", "src": [], "description": "Select a .zmt archive file to decompress"}, {"key": "output_dir", "value": "/custom/output/path", "description": "Optional custom output directory", "type": "text", "disabled": true}]}, "url": {"raw": "{{base_url}}/api/uncompress", "host": ["{{base_url}}"], "path": ["api", "uncompress"]}, "description": "Decompress a ZMT archive by uploading it.\n\n**Parameters:**\n- `archive_file` (file, required): ZMT archive file to decompress\n- `output_dir` (string, optional): Custom output directory path\n\n**Response:**\n```json\n{\n  \"status\": \"success\",\n  \"restored_path\": \"/tmp/compression_api/outputs/restored/archive_name\",\n  \"extracted_files\": 5,\n  \"total_size\": \"180.0MB\",\n  \"total_size_bytes\": 188743680\n}\n```"}, "response": []}, {"name": "Decompress Local Archive", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has decompression data\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData).to.have.property('restored_path');", "    pm.expect(jsonData.status).to.eql('success');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "archive_path", "value": "{{last_compressed_file}}", "description": "Path to the ZMT archive file"}, {"key": "output_dir", "value": "/custom/output/directory", "description": "Optional custom output directory", "disabled": true}]}, "url": {"raw": "{{base_url}}/api/uncompress", "host": ["{{base_url}}"], "path": ["api", "uncompress"]}, "description": "Decompress a ZMT archive by specifying its local path.\n\n**Parameters:**\n- `archive_path` (string, required): Absolute path to ZMT archive file\n- `output_dir` (string, optional): Custom output directory path\n\n**Example:**\n- `archive_path`: `/tmp/compression_api/outputs/my_file.zmt`\n\n**Response:**\n```json\n{\n  \"status\": \"success\",\n  \"restored_path\": \"/tmp/compression_api/outputs/restored/my_file\",\n  \"extracted_files\": 3,\n  \"total_size\": \"1.5MB\",\n  \"total_size_bytes\": 1572864\n}\n```\n\n**Note:** This example uses the `{{last_compressed_file}}` variable set by the compression test."}, "response": []}], "description": "Endpoints for decompressing ZMT archives and restoring files"}, {"name": "Archive Management", "item": [{"name": "List Archive Contents", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has archive info\", function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "    pm.expect(jsonData).to.have.property('archive_path');", "    pm.expect(jsonData.status).to.eql('success');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/archive/contents?archive_path={{last_compressed_file}}", "host": ["{{base_url}}"], "path": ["api", "archive", "contents"], "query": [{"key": "archive_path", "value": "{{last_compressed_file}}", "description": "Path to the ZMT archive file"}]}, "description": "Get information about a ZMT archive without extracting it.\n\n**Parameters:**\n- `archive_path` (string, required): Path to the ZMT archive file\n\n**Response:**\n```json\n{\n  \"status\": \"success\",\n  \"archive_path\": \"/path/to/archive.zmt\",\n  \"archive_size\": \"1.1KB\",\n  \"archive_size_bytes\": 1127,\n  \"file_info\": \"ZMT archive\",\n  \"note\": \"ZMT archive - extract to see contents\"\n}\n```\n\n**Note:** ZMT doesn't provide direct content listing, so this returns basic archive information."}, "response": []}], "description": "Endpoints for managing and inspecting ZMT archives"}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}]}