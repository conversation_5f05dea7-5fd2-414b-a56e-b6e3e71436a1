#!/bin/bash

# ZMT Compression Script Wrapper
# Usage: ./zmt.sh <input_path> <output_path> [max_compress]
# This script wraps the actual ./zmt compression tool

set -e  # Exit on any error

INPUT_PATH="$1"
OUTPUT_PATH="$2"
MAX_COMPRESS="$3"

# Validate arguments
if [ $# -lt 2 ] || [ $# -gt 3 ]; then
    echo "Usage: $0 <input_path> <output_path> [max_compress]"
    echo "  max_compress: 'true' to enable maximum compression (-m5), 'false' or omit for default"
    exit 1
fi

if [ ! -e "$INPUT_PATH" ]; then
    echo "Error: Input path '$INPUT_PATH' does not exist"
    exit 1
fi

# Check if zmt binary exists
if [ ! -f "./zmt" ]; then
    echo "Error: ZMT compression binary './zmt' not found"
    exit 1
fi

# Get original size
if [ -f "$INPUT_PATH" ]; then
    ORIGINAL_SIZE=$(stat -f%z "$INPUT_PATH" 2>/dev/null || stat -c%s "$INPUT_PATH" 2>/dev/null)
elif [ -d "$INPUT_PATH" ]; then
    # Use find to calculate directory size on macOS
    ORIGINAL_SIZE=$(find "$INPUT_PATH" -type f -exec stat -f%z {} \; | awk '{sum += $1} END {print sum}')
else
    echo "Error: Input path is neither a file nor directory"
    exit 1
fi

# Create output directory if it doesn't exist
OUTPUT_DIR=$(dirname "$OUTPUT_PATH")
mkdir -p "$OUTPUT_DIR"

# Perform compression using ZMT
if [ "$MAX_COMPRESS" = "true" ]; then
    echo "Compressing '$INPUT_PATH' to '$OUTPUT_PATH' using ZMT with maximum compression (-m5)..."
    # Use ZMT compression with -m5 for maximum compression
    if ! ./zmt a "$OUTPUT_PATH" "$INPUT_PATH" -m5; then
        echo "Error: ZMT compression with -m5 failed"
        exit 1
    fi
else
    echo "Compressing '$INPUT_PATH' to '$OUTPUT_PATH' using ZMT with default compression..."
    # Use ZMT compression with default settings
    if ! ./zmt a "$OUTPUT_PATH" "$INPUT_PATH"; then
        echo "Error: ZMT compression failed"
        exit 1
    fi
fi

# Verify output file was created
if [ ! -f "$OUTPUT_PATH" ]; then
    echo "Error: Compression failed - output file not created"
    exit 1
fi

# Get compressed size
COMPRESSED_SIZE=$(stat -f%z "$OUTPUT_PATH" 2>/dev/null || stat -c%s "$OUTPUT_PATH" 2>/dev/null)

# Calculate compression ratio
if command -v bc >/dev/null 2>&1 && [ "$ORIGINAL_SIZE" -gt 0 ]; then
    RATIO=$(echo "scale=2; (1 - $COMPRESSED_SIZE / $ORIGINAL_SIZE) * 100" | bc -l 2>/dev/null || echo "0")
else
    # Fallback calculation using awk
    if [ "$ORIGINAL_SIZE" -gt 0 ]; then
        RATIO=$(awk "BEGIN {printf \"%.2f\", (1 - $COMPRESSED_SIZE / $ORIGINAL_SIZE) * 100}")
    else
        RATIO="0"
    fi
fi

# Output results in a format that can be parsed by Python
echo "COMPRESSION_SUCCESS"
echo "ORIGINAL_SIZE:$ORIGINAL_SIZE"
echo "COMPRESSED_SIZE:$COMPRESSED_SIZE"
echo "COMPRESSION_RATIO:$RATIO"
echo "OUTPUT_PATH:$OUTPUT_PATH"

echo "ZMT compression completed successfully!"
